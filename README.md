# FX Backtester - Trading Simulation Web App

Eine professionelle Web-Anwendung für das Backtesting von Forex-Trading-Strategien mit historischen MetaTrader-Daten.

## 🚀 Features

### 📊 **CSV-Import für MetaTrader Daten**
- Drag & Drop Interface für CSV-Dateien
- Automatische Validierung des MetaTrader-Formats
- Unterstützt Format: `DATE, TIME, OPEN, HIGH, LOW, CLOSE, TICKVOL, VOL, SPREAD`
- Fehlerbehandlung und detailliertes Parsing-Feedback

### 📈 **Interaktive Candlestick-Charts**
- Powered by TradingView's Lightweight Charts
- Echtzeit-Anzeige der aktuellen Kerze
- Automatisches Scrollen und Zoom
- Professionelle Trading-Visualisierung

### ⏯️ **Historische Kurs-Wiedergabe (FX Replay)**
- Play/Pause/Step-Forward/Step-Backward Kontrollen
- Einstellbare Wiedergabegeschwindigkeit (100ms - 2s)
- Progress Bar mit aktueller Position
- Präzise Navigation durch historische Daten

### 💰 **Virtuelles Trading System**
- Buy/Sell Order-Platzierung mit einem Klick
- Stop Loss und Take Profit Unterstützung
- Automatische Margin-Berechnung (1:100 Leverage)
- Echtzeit P&L Tracking
- Automatische Order-Ausführung bei SL/TP

### 📋 **Portfolio-Management**
- Live Account-Übersicht (Balance, Equity, P&L)
- Offene Positionen mit Unrealized P&L
- Order-Historie mit Performance-Tracking
- Detaillierte Performance-Metriken

### 📊 **Performance-Analyse**
- Win Rate und Profit Factor
- Average Win/Loss Berechnung
- Maximum Drawdown Tracking
- Sharpe Ratio Berechnung
- Automatische Performance-Bewertung

## 🛠️ Technische Details

- **Framework:** Next.js 15 mit TypeScript
- **Styling:** Tailwind CSS
- **Charts:** TradingView Lightweight Charts
- **CSV Processing:** Papa Parse
- **Icons:** Lucide React
- **State Management:** React Context + useReducer

## 🚀 Installation & Start

1. **Dependencies installieren:**
   ```bash
   npm install
   ```

2. **Development Server starten:**
   ```bash
   npm run dev
   ```

3. **App öffnen:**
   ```
   http://localhost:3000
   ```

## 📝 Verwendung

### 1. **CSV-Datei importieren**
- Ziehe deine MetaTrader CSV-Datei in den Import-Bereich
- Oder klicke zum Durchsuchen deiner Dateien
- Die App validiert automatisch das Format

### 2. **Trading starten**
- Klicke auf "Start Trading" nach erfolgreichem Import
- Die Chart-Ansicht öffnet sich mit deinen Daten

### 3. **Kurse abspielen**
- Verwende die Playback-Kontrollen um durch die historischen Daten zu navigieren
- Stelle die Geschwindigkeit nach deinen Wünschen ein
- Nutze Step-Forward/Backward für präzise Kontrolle

### 4. **Orders platzieren**
- Nutze das Order-Panel um Buy/Sell Orders zu platzieren
- Setze optional Stop Loss und Take Profit
- Die App berechnet automatisch Required Margin

### 5. **Performance verfolgen**
- Beobachte deine Trades und P&L in Echtzeit
- Analysiere deine Performance-Metriken
- Verfolge offene Positionen und Order-Historie

## 📄 CSV-Format

Die App erwartet MetaTrader CSV-Dateien im folgenden Format:

```csv
DATE,TIME,OPEN,HIGH,LOW,CLOSE,TICKVOL,VOL,SPREAD
2025.01.20,09:00:00,1.11783,1.11790,1.11775,1.11785,15,0,2
2025.01.20,09:01:00,1.11785,1.11795,1.11780,1.11792,12,0,2
```

### Spalten-Beschreibung:
- **DATE:** Datum im Format YYYY.MM.DD
- **TIME:** Zeit im Format HH:MM:SS
- **OPEN:** Eröffnungskurs
- **HIGH:** Höchstkurs
- **LOW:** Tiefstkurs
- **CLOSE:** Schlusskurs
- **TICKVOL:** Tick-Volumen
- **VOL:** Volumen
- **SPREAD:** Spread in Punkten

## 🎯 Beispiel-Daten

Eine Beispiel-CSV-Datei (`sample_data.csv`) ist im Projekt enthalten zum Testen der Funktionalität.

## 🔧 Trading-Parameter

### Standard-Einstellungen:
- **Start-Balance:** $10,000
- **Leverage:** 1:100
- **Commission:** $7 pro Lot
- **Margin Requirement:** 1%
- **Lot Size:** 100,000 Einheiten

### Unterstützte Order-Größen:
- Minimum: 0.01 Lots (Micro Lot)
- Maximum: 100 Lots
- Standard-Größen: 0.01, 0.1, 0.5, 1.0 Lots

## 📊 Performance-Metriken

Die App berechnet automatisch:
- **Total Return:** Gesamtrendite in %
- **Win Rate:** Gewinnrate in %
- **Profit Factor:** Verhältnis Gewinne/Verluste
- **Average Win/Loss:** Durchschnittliche Gewinne/Verluste
- **Maximum Drawdown:** Maximaler Verlust
- **Sharpe Ratio:** Risiko-adjustierte Rendite

## 🎨 Features im Detail

### Chart-Funktionen:
- Zoom und Pan
- Crosshair mit Preis-Info
- Order-Marker (Entry/Exit)
- Stop Loss/Take Profit Linien
- Current Price Line

### Order-Management:
- Market Orders (Buy/Sell)
- Stop Loss Orders
- Take Profit Orders
- Position Sizing
- Margin-Berechnung

### Risk Management:
- Automatische Margin-Prüfung
- Stop Loss/Take Profit Ausführung
- Real-time P&L Berechnung
- Portfolio-Überwachung

## 🚀 Erweiterte Features (Geplant)

- [ ] Multiple Timeframes
- [ ] Technical Indicators
- [ ] Strategy Backtesting
- [ ] Export/Import von Trading-Sessions
- [ ] Advanced Order Types
- [ ] Multi-Symbol Trading
- [ ] Performance Reports (PDF)

---

**Happy Trading! 📈💰**
