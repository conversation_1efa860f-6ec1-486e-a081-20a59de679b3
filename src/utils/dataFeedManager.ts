import { CandleData, Timeframe, DataFeedState, UpdateMode } from '@/types/trading';
import { TIMEFRAMES } from './timeframeAggregator';

/**
 * DataFeedManager handles progressive data revelation for realistic trading simulation.
 * It ensures that only historical data up to the current simulation time is available,
 * preventing future data leakage that would break the trading simulation illusion.
 */
export class DataFeedManager {
  private rawData: CandleData[];
  private baseData: CandleData[]; // Original M1 data for intra-candle updates
  private currentIndex: number;
  private bufferSize: number;
  private timeframe: Timeframe;
  private updateMode: UpdateMode;
  private intraCandleStep: number;
  private intraCandleSteps: number;
  private isTickData: boolean; // Flag to indicate if working with tick-generated data

  constructor(
    data: CandleData[],
    baseData: CandleData[],
    timeframe: Timeframe = 'M1',
    updateMode: UpdateMode = 'complete',
    bufferSize: number = 200,
    isTickData: boolean = false
  ) {
    this.rawData = data;
    this.baseData = baseData;
    this.currentIndex = 0;
    this.bufferSize = bufferSize;
    this.timeframe = timeframe;
    this.updateMode = updateMode;
    this.intraCandleStep = 0;
    this.intraCandleSteps = this.calculateIntraCandleSteps();
    this.isTickData = isTickData;
  }

  /**
   * Calculate how many intra-candle steps are needed for current timeframe
   */
  private calculateIntraCandleSteps(): number {
    if (this.updateMode === 'complete' || this.timeframe === 'M1') {
      return 1;
    }

    // For intra-candle mode, use the timeframe seconds converted to steps
    // For seconds timeframes, use 1 step per second up to 60 steps max
    // For minute+ timeframes, use minutes as steps (legacy behavior)
    const seconds = TIMEFRAMES[this.timeframe].seconds;
    if (seconds < 60) {
      return Math.min(seconds, 60); // Max 60 steps for sub-minute timeframes
    } else {
      return Math.min(Math.floor(seconds / 60), 60); // Convert to minutes, max 60 steps
    }
  }

  /**
   * Get the current state of the data feed
   */
  getState(): DataFeedState {
    return {
      currentIndex: this.currentIndex,
      visibleData: this.getVisibleData(),
      bufferSize: this.bufferSize,
      timeframe: this.timeframe,
      updateMode: this.updateMode,
      intraCandleStep: this.intraCandleStep,
      intraCandleSteps: this.intraCandleSteps
    };
  }

  /**
   * Set the current index (simulation time)
   */
  setCurrentIndex(index: number): void {
    this.currentIndex = Math.max(0, Math.min(index, this.rawData.length - 1));
  }

  /**
   * Set the current intra-candle step
   */
  setIntraCandleStep(step: number): void {
    this.intraCandleStep = Math.max(0, Math.min(step, this.intraCandleSteps - 1));
  }

  /**
   * Get the current candle at the simulation time
   */
  getCurrentCandle(): CandleData | null {
    if (this.currentIndex >= this.rawData.length || this.currentIndex < 0) {
      return null;
    }
    return this.rawData[this.currentIndex];
  }

  /**
   * Get only the historical data that should be visible at current simulation time.
   * This is the key method that prevents future data leakage.
   */
  getVisibleData(): CandleData[] {
    if (this.rawData.length === 0 || this.currentIndex < 0) {
      return [];
    }

    // Only return data up to and including the current index
    const endIndex = this.currentIndex + 1; // +1 because slice is exclusive
    const startIndex = Math.max(0, endIndex - this.bufferSize);

    return this.rawData.slice(startIndex, endIndex);
  }

  /**
   * Get the next candle in the sequence (for progressive revelation)
   */
  getNextCandle(): CandleData | null {
    if (this.currentIndex >= this.rawData.length - 1) {
      return null;
    }

    this.currentIndex++;
    return this.rawData[this.currentIndex];
  }

  /**
   * Check if we can advance to the next candle
   */
  canAdvance(): boolean {
    return this.currentIndex < this.rawData.length - 1;
  }

  /**
   * Check if we can go back to previous candle
   */
  canGoBack(): boolean {
    return this.currentIndex > 0;
  }

  /**
   * Go back to previous candle
   */
  getPreviousCandle(): CandleData | null {
    if (this.currentIndex <= 0) {
      return null;
    }

    this.currentIndex--;
    return this.rawData[this.currentIndex];
  }

  /**
   * Reset to the beginning
   */
  reset(): void {
    this.currentIndex = 0;
  }

  /**
   * Get total number of candles
   */
  getTotalCandles(): number {
    return this.rawData.length;
  }

  /**
   * Get current progress as percentage
   */
  getProgress(): number {
    if (this.rawData.length === 0) return 0;
    return (this.currentIndex / (this.rawData.length - 1)) * 100;
  }

  /**
   * Set buffer size (how many candles to keep in memory)
   */
  setBufferSize(size: number): void {
    this.bufferSize = Math.max(50, size); // Minimum 50 candles
  }

  /**
   * Get data for chart initialization (only up to current index)
   */
  getChartData(): any[] {
    return this.getVisibleData().map(candle => ({
      time: Math.floor(candle.timestamp / 1000),
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close
    }));
  }

  /**
   * Get the latest candle for real-time updates
   * In intra-candle mode, this builds the candle progressively
   */
  getLatestCandleForChart(): any | null {
    const currentCandle = this.getCurrentCandle();
    if (!currentCandle) return null;

    // Always return complete candle in complete mode
    if (this.updateMode === 'complete') {
      return {
        time: Math.floor(currentCandle.timestamp / 1000),
        open: currentCandle.open,
        high: currentCandle.high,
        low: currentCandle.low,
        close: currentCandle.close
      };
    }

    // For intra-candle mode with tick data: let main component handle all timeframes
    // The main component has the tick data and can build candles progressively
    if (this.isTickData) {
      return {
        time: Math.floor(currentCandle.timestamp / 1000),
        open: currentCandle.open,
        high: currentCandle.high,
        low: currentCandle.low,
        close: currentCandle.close
      };
    }

    // For intra-candle mode with regular candle data: use DataFeedManager logic
    return this.getIntraCandleUpdate(currentCandle);
  }

  /**
   * Get intra-candle update based on current step
   */
  private getIntraCandleUpdate(targetCandle: CandleData): any {
    if (this.intraCandleStep === 0) {
      // First step: only show opening price
      return {
        time: Math.floor(targetCandle.timestamp / 1000),
        open: targetCandle.open,
        high: targetCandle.open,
        low: targetCandle.open,
        close: targetCandle.open
      };
    }

    // Calculate progress through the candle (0 to 1)
    const progress = this.intraCandleStep / (this.intraCandleSteps - 1);

    // Get the underlying M1 data for this timeframe period
    const intraCandleData = this.getIntraCandleM1Data(targetCandle);

    if (intraCandleData.length === 0) {
      // Fallback to interpolated values
      return this.getInterpolatedCandle(targetCandle, progress);
    }

    // Use actual M1 data to build the candle progressively
    const stepsToInclude = Math.ceil(intraCandleData.length * progress);
    const includedData = intraCandleData.slice(0, stepsToInclude);

    if (includedData.length === 0) {
      return {
        time: Math.floor(targetCandle.timestamp / 1000),
        open: targetCandle.open,
        high: targetCandle.open,
        low: targetCandle.open,
        close: targetCandle.open
      };
    }

    // Calculate OHLC from included base data
    const open = includedData[0].open;
    const close = includedData[includedData.length - 1].close;
    const high = Math.max(...includedData.map(d => d.high));
    const low = Math.min(...includedData.map(d => d.low));

    // Validate OHLC values before returning
    if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close) ||
        !isFinite(open) || !isFinite(high) || !isFinite(low) || !isFinite(close)) {
      // Fallback to target candle values
      return {
        time: Math.floor(targetCandle.timestamp / 1000),
        open: targetCandle.open,
        high: targetCandle.high,
        low: targetCandle.low,
        close: targetCandle.close
      };
    }

    return {
      time: Math.floor(targetCandle.timestamp / 1000),
      open,
      high,
      low,
      close
    };
  }

  /**
   * Get base data that belongs to the current timeframe candle
   */
  private getIntraCandleM1Data(targetCandle: CandleData): CandleData[] {
    const timeframeSeconds = TIMEFRAMES[this.timeframe].seconds;
    const candleStartTime = targetCandle.timestamp;
    const candleEndTime = candleStartTime + (timeframeSeconds * 1000);

    return this.baseData.filter(baseCandle =>
      baseCandle.timestamp >= candleStartTime &&
      baseCandle.timestamp < candleEndTime
    );
  }

  /**
   * Fallback: interpolate candle values when M1 data is not available
   */
  private getInterpolatedCandle(targetCandle: CandleData, progress: number): any {
    const open = targetCandle.open;
    const close = targetCandle.close;
    const high = targetCandle.high;
    const low = targetCandle.low;

    // Simple interpolation between open and close
    const currentPrice = open + (close - open) * progress;

    // Progressive high/low revelation
    const currentHigh = Math.max(open, open + (high - open) * progress);
    const currentLow = Math.min(open, open + (low - open) * progress);

    return {
      time: Math.floor(targetCandle.timestamp / 1000),
      open,
      high: currentHigh,
      low: currentLow,
      close: currentPrice
    };
  }

  /**
   * Check if this is a new candle compared to previous state
   */
  isNewCandle(previousIndex: number): boolean {
    return this.currentIndex > previousIndex;
  }

  /**
   * Get visible range for chart (start and end timestamps)
   */
  getVisibleRange(): { from: number; to: number } | null {
    const visibleData = this.getVisibleData();
    if (visibleData.length === 0) return null;

    return {
      from: Math.floor(visibleData[0].timestamp / 1000),
      to: Math.floor(visibleData[visibleData.length - 1].timestamp / 1000)
    };
  }

  /**
   * Update raw data (for timeframe switching)
   */
  updateData(newData: CandleData[], preservePosition: boolean = true): void {
    const oldTimestamp = preservePosition && this.rawData[this.currentIndex]
      ? this.rawData[this.currentIndex].timestamp
      : null;

    this.rawData = newData;

    if (preservePosition && oldTimestamp) {
      // Find closest timestamp in new data
      let closestIndex = 0;
      let minDiff = Math.abs(newData[0]?.timestamp - oldTimestamp);

      for (let i = 1; i < newData.length; i++) {
        const diff = Math.abs(newData[i].timestamp - oldTimestamp);
        if (diff < minDiff) {
          minDiff = diff;
          closestIndex = i;
        }
      }

      this.currentIndex = closestIndex;
    } else {
      this.currentIndex = 0;
    }
  }

  /**
   * Advance to next step (either next candle or next intra-candle step)
   */
  advanceStep(): boolean {
    if (this.updateMode === 'complete' || this.timeframe === 'M1') {
      // Standard mode: advance to next candle
      if (this.currentIndex >= this.rawData.length - 1) {
        return false;
      }
      this.currentIndex++;
      this.intraCandleStep = 0;
      return true;
    } else {
      // Intra-candle mode: advance step within current candle
      if (this.intraCandleStep < this.intraCandleSteps - 1) {
        this.intraCandleStep++;
        return true;
      } else {
        // Move to next candle and reset step
        if (this.currentIndex >= this.rawData.length - 1) {
          return false;
        }
        this.currentIndex++;
        this.intraCandleStep = 0;
        return true;
      }
    }
  }

  /**
   * Go back one step
   */
  goBackStep(): boolean {
    if (this.updateMode === 'complete' || this.timeframe === 'M1') {
      // Standard mode: go back to previous candle
      if (this.currentIndex <= 0) {
        return false;
      }
      this.currentIndex--;
      this.intraCandleStep = 0;
      return true;
    } else {
      // Intra-candle mode: go back one step
      if (this.intraCandleStep > 0) {
        this.intraCandleStep--;
        return true;
      } else {
        // Move to previous candle and set to last step
        if (this.currentIndex <= 0) {
          return false;
        }
        this.currentIndex--;
        this.intraCandleStep = this.intraCandleSteps - 1;
        return true;
      }
    }
  }

  /**
   * Check if we can advance to next step
   */
  canAdvanceStep(): boolean {
    if (this.updateMode === 'complete' || this.timeframe === 'M1') {
      return this.currentIndex < this.rawData.length - 1;
    } else {
      return this.currentIndex < this.rawData.length - 1 ||
             this.intraCandleStep < this.intraCandleSteps - 1;
    }
  }

  /**
   * Check if we can go back one step
   */
  canGoBackStep(): boolean {
    if (this.updateMode === 'complete' || this.timeframe === 'M1') {
      return this.currentIndex > 0;
    } else {
      return this.currentIndex > 0 || this.intraCandleStep > 0;
    }
  }

  /**
   * Set update mode
   */
  setUpdateMode(mode: UpdateMode): void {
    this.updateMode = mode;
    this.intraCandleSteps = this.calculateIntraCandleSteps();
    this.intraCandleStep = 0;
  }

  /**
   * Get current update mode
   */
  getUpdateMode(): UpdateMode {
    return this.updateMode;
  }

  /**
   * Get current intra-candle progress info
   */
  getIntraCandleProgress(): { step: number; total: number; progress: number } {
    return {
      step: this.intraCandleStep + 1,
      total: this.intraCandleSteps,
      progress: this.intraCandleSteps > 1 ? this.intraCandleStep / (this.intraCandleSteps - 1) : 1
    };
  }

  /**
   * Set timeframe
   */
  setTimeframe(timeframe: Timeframe): void {
    this.timeframe = timeframe;
    this.intraCandleSteps = this.calculateIntraCandleSteps();
    this.intraCandleStep = 0;
  }

  /**
   * Get current timeframe
   */
  getTimeframe(): Timeframe {
    return this.timeframe;
  }

  /**
   * Update base data (for timeframe switching)
   */
  updateBaseData(newBaseData: CandleData[]): void {
    this.baseData = newBaseData;
  }
}
