'use client';

import React from 'react';
import { BarChart3, TrendingUp } from 'lucide-react';
import { UpdateMode, UpdateModeInfo, Timeframe } from '@/types/trading';

interface UpdateModeSelectorProps {
  currentMode: UpdateMode;
  currentTimeframe: Timeframe;
  onModeChange: (mode: UpdateMode) => void;
  disabled?: boolean;
  intraCandleProgress?: { step: number; total: number; progress: number };
  dataType?: 'candle' | 'tick';
}

const UPDATE_MODES: UpdateModeInfo[] = [
  {
    value: 'complete',
    label: 'Complete Candles',
    description: 'Show complete candles at once'
  },
  {
    value: 'intraCandle',
    label: 'Intra-Candle',
    description: 'Show candle formation step by step'
  }
];

export default function UpdateModeSelector({
  currentMode,
  currentTimeframe,
  onModeChange,
  disabled = false,
  intraCandleProgress,
  dataType = 'candle'
}: UpdateModeSelectorProps) {
  // Intra-candle mode availability logic
  const isIntraCandleAvailable = dataType === 'tick'
    ? true // For tick data, intra-candle is always available
    : currentTimeframe !== 'M1'; // For candle data, only for timeframes > M1

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        <TrendingUp className="h-4 w-4 text-trading-text-secondary" />
        <span className="text-sm font-medium text-trading-text-primary">Update Mode:</span>
      </div>

      <div className="flex items-center space-x-1">
        {UPDATE_MODES.map((mode) => {
          const isDisabled = disabled || (mode.value === 'intraCandle' && !isIntraCandleAvailable);
          const isActive = currentMode === mode.value;

          return (
            <button
              key={mode.value}
              onClick={() => onModeChange(mode.value)}
              disabled={isDisabled}
              className={`
                px-3 py-1 text-xs font-medium rounded transition-all duration-200 flex items-center space-x-1
                ${isActive
                  ? 'bg-trading-accent text-white shadow-sm'
                  : 'bg-trading-surface text-trading-text-secondary hover:bg-trading-border hover:text-trading-text-primary'
                }
                ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
              title={isDisabled && mode.value === 'intraCandle'
                ? dataType === 'tick'
                  ? 'Intra-candle mode shows tick-by-tick candle formation'
                  : 'Intra-candle mode only available for timeframes > M1'
                : mode.description
              }
            >
              {mode.value === 'complete' ? (
                <BarChart3 className="h-3 w-3" />
              ) : (
                <TrendingUp className="h-3 w-3" />
              )}
              <span>{mode.label}</span>
            </button>
          );
        })}
      </div>

      {/* Intra-candle progress indicator */}
      {currentMode === 'intraCandle' && isIntraCandleAvailable && intraCandleProgress && (
        <div className="flex items-center space-x-2 text-xs text-trading-text-secondary">
          <span>Step:</span>
          <div className="flex items-center space-x-1">
            <span className="text-trading-text-primary font-mono">
              {intraCandleProgress.step}/{intraCandleProgress.total}
            </span>
            <div className="w-16 h-1 bg-trading-surface rounded-full overflow-hidden">
              <div
                className="h-full bg-trading-accent transition-all duration-200"
                style={{ width: `${intraCandleProgress.progress * 100}%` }}
              />
            </div>
            <span className="text-trading-text-primary font-mono">
              {(intraCandleProgress.progress * 100).toFixed(0)}%
            </span>
          </div>
        </div>
      )}

      {/* Info text for current mode */}
      <div className="text-xs text-trading-text-muted">
        {currentMode === 'complete'
          ? dataType === 'tick'
            ? 'Complete ticks advance instantly'
            : 'Complete candles appear instantly'
          : dataType === 'tick'
          ? 'Candle forms tick-by-tick from real market data'
          : currentTimeframe === 'M1'
          ? 'M1 timeframe always shows complete candles'
          : `Candle forms in ${currentTimeframe === 'M5' ? '5' :
                              currentTimeframe === 'M15' ? '15' :
                              currentTimeframe === 'M30' ? '30' :
                              currentTimeframe === 'H1' ? '60' :
                              currentTimeframe === 'H4' ? '240' : 'multiple'} steps`
        }
      </div>
    </div>
  );
}
