'use client';

import React from 'react';
import { TrendingUp, Target, Award } from 'lucide-react';
import { Order } from '@/types/trading';
import { calculatePerformanceMetrics, formatCurrency, formatPercentage } from '@/utils/tradingCalculations';

interface PerformancePanelProps {
  orders: Order[];
  startBalance: number;
  currentBalance: number;
}

export default function PerformancePanel({ orders, startBalance, currentBalance }: PerformancePanelProps) {
  const metrics = calculatePerformanceMetrics(orders);
  const totalReturn = ((currentBalance - startBalance) / startBalance) * 100;

  const StatCard = ({
    icon: Icon,
    title,
    value,
    subtitle,
    color = 'text-trading-text-primary'
  }: {
    icon: React.ElementType;
    title: string;
    value: string;
    subtitle?: string;
    color?: string;
  }) => (
    <div className="bg-trading-surface p-4 rounded-lg border border-trading-border hover:border-trading-info/50 transition-colors">
      <div className="flex items-center justify-between mb-3">
        <Icon className={`h-5 w-5 ${color}`} />
        <span className="text-xs text-trading-text-secondary font-medium">{title}</span>
      </div>
      <div className={`text-xl font-bold ${color}`}>{value}</div>
      {subtitle && <div className="text-xs text-trading-text-secondary mt-2">{subtitle}</div>}
    </div>
  );

  if (orders.length === 0) {
    return (
      <div className="trading-card">
        <h3 className="text-lg font-semibold text-trading-text-primary mb-6 flex items-center">
          <div className="w-2 h-2 bg-trading-warning rounded-full mr-2"></div>
          Performance Metrics
        </h3>
        <div className="text-center text-trading-text-secondary py-8">
          <Target className="h-16 w-16 mx-auto mb-4 opacity-50 text-trading-text-muted" />
          <p className="text-lg font-medium text-trading-text-primary mb-2">No trades yet</p>
          <p className="text-sm">Start trading to see performance metrics and analytics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="trading-card">
      <h3 className="text-lg font-semibold text-trading-text-primary mb-6 flex items-center">
        <div className="w-2 h-2 bg-trading-warning rounded-full mr-2"></div>
        Performance Metrics
      </h3>

      {/* Overview */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <StatCard
          icon={TrendingUp}
          title="Total Return"
          value={formatPercentage(totalReturn)}
          subtitle={formatCurrency(currentBalance - startBalance)}
          color={totalReturn >= 0 ? 'stat-positive' : 'stat-negative'}
        />
        <StatCard
          icon={Award}
          title="Win Rate"
          value={formatPercentage(metrics.winRate)}
          subtitle={`${metrics.winningTrades}/${metrics.totalTrades} trades`}
          color={metrics.winRate >= 50 ? 'stat-positive' : 'stat-negative'}
        />
      </div>

      {/* Detailed metrics */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-sm">
            <div className="text-trading-text-secondary">Total Trades</div>
            <div className="font-semibold text-trading-text-primary">{metrics.totalTrades}</div>
          </div>
          <div className="text-sm">
            <div className="text-trading-text-secondary">Total P&L</div>
            <div className={`font-semibold ${metrics.totalPnL >= 0 ? 'stat-positive' : 'stat-negative'}`}>
              {formatCurrency(metrics.totalPnL)}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="text-sm">
            <div className="text-trading-text-secondary">Winning Trades</div>
            <div className="font-semibold stat-positive">{metrics.winningTrades}</div>
          </div>
          <div className="text-sm">
            <div className="text-trading-text-secondary">Losing Trades</div>
            <div className="font-semibold stat-negative">{metrics.losingTrades}</div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="text-sm">
            <div className="text-trading-text-secondary">Average Win</div>
            <div className="font-semibold stat-positive">
              {formatCurrency(metrics.averageWin)}
            </div>
          </div>
          <div className="text-sm">
            <div className="text-trading-text-secondary">Average Loss</div>
            <div className="font-semibold stat-negative">
              {formatCurrency(metrics.averageLoss)}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="text-sm">
            <div className="text-trading-text-secondary">Largest Win</div>
            <div className="font-semibold stat-positive">
              {formatCurrency(metrics.largestWin)}
            </div>
          </div>
          <div className="text-sm">
            <div className="text-trading-text-secondary">Largest Loss</div>
            <div className="font-semibold stat-negative">
              {formatCurrency(metrics.largestLoss)}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="text-sm">
            <div className="text-trading-text-secondary">Profit Factor</div>
            <div className={`font-semibold ${metrics.profitFactor >= 1 ? 'stat-positive' : 'stat-negative'}`}>
              {metrics.profitFactor.toFixed(2)}
            </div>
          </div>
          <div className="text-sm">
            <div className="text-trading-text-secondary">Max Drawdown</div>
            <div className="font-semibold stat-negative">
              {formatCurrency(metrics.maxDrawdown)}
            </div>
          </div>
        </div>

        {metrics.sharpeRatio !== 0 && (
          <div className="text-sm">
            <div className="text-trading-text-secondary">Sharpe Ratio</div>
            <div className={`font-semibold ${metrics.sharpeRatio >= 1 ? 'stat-positive' : 'stat-negative'}`}>
              {metrics.sharpeRatio.toFixed(2)}
            </div>
          </div>
        )}
      </div>

      {/* Performance summary */}
      <div className="mt-6 pt-4 border-t border-trading-border">
        <div className="text-xs text-trading-text-secondary mb-3 font-medium">Performance Summary</div>
        <div className="text-sm p-3 rounded-lg bg-trading-surface border border-trading-border">
          {metrics.totalTrades === 0 ? (
            <span className="text-trading-text-secondary">No trades completed</span>
          ) : metrics.winRate >= 60 && metrics.profitFactor >= 1.5 ? (
            <span className="stat-positive font-medium">🎯 Excellent performance!</span>
          ) : metrics.winRate >= 50 && metrics.profitFactor >= 1.2 ? (
            <span className="text-trading-info font-medium">👍 Good performance</span>
          ) : metrics.winRate >= 40 && metrics.profitFactor >= 1.0 ? (
            <span className="text-trading-warning font-medium">⚠️ Average performance</span>
          ) : (
            <span className="stat-negative font-medium">📉 Needs improvement</span>
          )}
        </div>
      </div>
    </div>
  );
}
