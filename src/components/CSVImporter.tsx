'use client';

import React, { useState, useCallback } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { parseMetaTraderCSV, validateCSVFile } from '@/utils/csvParser';
import { CSVParseResult } from '@/types/trading';

interface CSVImporterProps {
  onDataLoaded: (result: CSVParseResult) => void;
  isLoading?: boolean;
}

export default function CSVImporter({ onDataLoaded, isLoading = false }: CSVImporterProps) {
  const [dragActive, setDragActive] = useState(false);
  const [parseResult, setParseResult] = useState<CSVParseResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFile = useCallback(async (file: File) => {
    setIsProcessing(true);
    setParseResult(null);

    // Validate file
    const validationErrors = validateCSVFile(file);
    if (validationErrors.length > 0) {
      setParseResult({
        dataType: 'candle',
        data: [],
        errors: validationErrors
      });
      setIsProcessing(false);
      return;
    }

    try {
      const result = await parseMetaTraderCSV(file);
      setParseResult(result);

      if ((result.data?.length || result.tickData?.length) && result.errors.length === 0) {
        onDataLoaded(result);
      }
    } catch (error) {
      setParseResult({
        dataType: 'candle',
        data: [],
        errors: [`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`]
      });
    } finally {
      setIsProcessing(false);
    }
  }, [onDataLoaded]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, [handleFile]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  }, [handleFile]);

  const getStatusIcon = () => {
    if (isProcessing) {
      return <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>;
    }

    if (parseResult) {
      if (parseResult.errors.length > 0) {
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      }
      if (parseResult.data?.length || parseResult.tickData?.length) {
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      }
    }

    return <Upload className="h-8 w-8 text-gray-400" />;
  };

  const getStatusText = () => {
    if (isProcessing) {
      return 'Processing file...';
    }

    if (parseResult) {
      if (parseResult.errors.length > 0) {
        return `Error: ${parseResult.errors[0]}`;
      }
      if (parseResult.data?.length || parseResult.tickData?.length) {
        const count = parseResult.data?.length || parseResult.tickData?.length || 0;
        const dataTypeLabel = parseResult.dataType === 'tick' ? 'ticks' : 'candles';
        return `Successfully loaded ${count} ${dataTypeLabel}${parseResult.symbol ? ` for ${parseResult.symbol}` : ''}`;
      }
    }

    return 'Drop your MetaTrader CSV file here or click to browse';
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
          ${dragActive ? 'border-trading-info bg-trading-info/10 scale-105' : 'border-trading-border'}
          ${isProcessing || isLoading ? 'opacity-50 pointer-events-none' : 'hover:border-trading-info/50 hover:bg-trading-surface/50'}
          bg-trading-secondary backdrop-blur-sm
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept=".csv"
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isProcessing || isLoading}
        />

        <div className="flex flex-col items-center space-y-6">
          {getStatusIcon()}

          <div>
            <p className="text-xl font-semibold text-trading-text-primary mb-2">
              {getStatusText()}
            </p>
            <p className="text-sm text-trading-text-secondary">
              Candle format: DATE, TIME, OPEN, HIGH, LOW, CLOSE, TICKVOL, VOL, SPREAD<br/>
              Tick format: DATE, TIME, BID, ASK, LAST, VOLUME, FLAGS
            </p>
          </div>
        </div>
      </div>

      {/* File format example */}
      <div className="mt-6 trading-panel p-4">
        <h3 className="text-sm font-medium text-trading-text-primary mb-3 flex items-center">
          <FileText className="h-4 w-4 mr-2 text-trading-info" />
          Expected CSV Formats
        </h3>
        <div className="text-xs font-mono text-trading-text-secondary bg-trading-surface p-3 rounded-lg border border-trading-border overflow-x-auto space-y-3">
          <div>
            <div className="text-trading-info mb-1">Candle Data (OHLC):</div>
            <div className="whitespace-nowrap">
              2025.05.19&nbsp;&nbsp;&nbsp;&nbsp;00:05:00&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1&nbsp;&nbsp;&nbsp;&nbsp;0&nbsp;&nbsp;&nbsp;&nbsp;42
            </div>
            <div className="whitespace-nowrap">
              2025.05.19&nbsp;&nbsp;&nbsp;&nbsp;00:06:00&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1.11783&nbsp;&nbsp;&nbsp;&nbsp;1.11773&nbsp;&nbsp;&nbsp;&nbsp;1.11774&nbsp;&nbsp;&nbsp;&nbsp;3&nbsp;&nbsp;&nbsp;&nbsp;0&nbsp;&nbsp;&nbsp;&nbsp;2
            </div>
          </div>
          <div>
            <div className="text-trading-success mb-1">Tick Data:</div>
            <div className="whitespace-nowrap">
              2025.05.21&nbsp;&nbsp;&nbsp;&nbsp;11:00:00.054&nbsp;&nbsp;&nbsp;&nbsp;1.13234&nbsp;&nbsp;&nbsp;&nbsp;1.13234&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6
            </div>
            <div className="whitespace-nowrap">
              2025.05.21&nbsp;&nbsp;&nbsp;&nbsp;11:00:01.398&nbsp;&nbsp;&nbsp;&nbsp;1.13239&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2
            </div>
          </div>
        </div>
      </div>

      {/* Parse results */}
      {parseResult && (
        <div className="mt-6 space-y-4">
          {(parseResult.data?.length || parseResult.tickData?.length) && (
            <div className="p-4 bg-trading-success/10 border border-trading-success/30 rounded-lg animate-fade-in">
              <h3 className="text-sm font-medium text-trading-success mb-3 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Successfully Parsed
              </h3>
              <ul className="text-sm text-trading-text-primary space-y-2">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-trading-success rounded-full mr-2"></span>
                  {parseResult.data?.length || parseResult.tickData?.length} {parseResult.dataType === 'tick' ? 'ticks' : 'candles'} loaded
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-trading-accent rounded-full mr-2"></span>
                  Data type: {parseResult.dataType === 'tick' ? 'Tick Data' : 'Candle Data (OHLC)'}
                </li>
                {parseResult.symbol && (
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-trading-info rounded-full mr-2"></span>
                    Symbol: {parseResult.symbol}
                  </li>
                )}
                {parseResult.precision !== undefined && (
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-trading-success rounded-full mr-2"></span>
                    Price precision: {parseResult.precision} decimal places
                  </li>
                )}
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-trading-warning rounded-full mr-2"></span>
                  Date range: {(parseResult.data?.[0] || parseResult.tickData?.[0])?.date} to {(parseResult.data?.[parseResult.data.length - 1] || parseResult.tickData?.[parseResult.tickData.length - 1])?.date}
                </li>
              </ul>
            </div>
          )}

          {parseResult.errors.length > 0 && (
            <div className="p-4 bg-trading-danger/10 border border-trading-danger/30 rounded-lg animate-fade-in">
              <h3 className="text-sm font-medium text-trading-danger mb-3 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                Parsing Errors ({parseResult.errors.length})
              </h3>
              <ul className="text-sm text-trading-text-secondary space-y-1 max-h-32 overflow-y-auto">
                {parseResult.errors.slice(0, 10).map((error, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-1 h-1 bg-trading-danger rounded-full mr-2 mt-2 flex-shrink-0"></span>
                    {error}
                  </li>
                ))}
                {parseResult.errors.length > 10 && (
                  <li className="flex items-start text-trading-text-muted">
                    <span className="w-1 h-1 bg-trading-text-muted rounded-full mr-2 mt-2 flex-shrink-0"></span>
                    ... and {parseResult.errors.length - 10} more errors
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
